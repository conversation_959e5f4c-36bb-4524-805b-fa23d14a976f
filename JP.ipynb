{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7dbf22f8-0594-41a4-9d4f-a959fefaaa05", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-12-21 06:27:07.154618: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2024-12-21 06:27:07.180052: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: SSE4.1 SSE4.2 AVX AVX2 AVX512F AVX512_VNNI AVX512_BF16 AVX512_FP16 AVX_VNNI AMX_TILE AMX_INT8 AMX_BF16 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n"]}], "source": ["import torch\n", "from transformers import ResNetForImageClassification\n", "from torchvision import datasets, transforms\n", "from torchvision import transforms\n", "from torch.utils.data import DataLoader\n", "import tensorflow as tf\n", "import tensorflow_hub as hub\n", "\n", "import tensorflow_datasets as tfds\n", "\n", "import time\n", "\n", "from PIL import Image\n", "import requests\n", "\n", "import numpy as np\n", "\n", "import os\n", "import pathlib"]}, {"cell_type": "code", "execution_count": 2, "id": "99320bb1-d227-40ee-b747-5a46a45ed3c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 6443 files belonging to 17 classes.\n", "Found 413 files belonging to 17 classes.\n", "Found 379 files belonging to 17 classes.\n", "Class names: ['Beet Armyworm', '<PERSON> Hairy', '<PERSON><PERSON>', '<PERSON> Cricket', 'Jute Aphid', 'Jute Hairy', 'Jute Red Mite', 'Jute Semilooper', 'Jute Stem Girdler', 'Jute Stem Weevil', 'Leaf Beetle', '<PERSON><PERSON>bu<PERSON>', 'Pod Borer', 'Scop<PERSON> Emissaria', '<PERSON>rmite', 'Termite odontotermes (Rambur)', 'Yellow Mite']\n", "Number of training samples: 6443\n", "Number of testing samples: 379\n", "Number of validation samples: 413\n"]}], "source": ["# Define dataset directories\n", "train_dir = 'LearningProjects/Jute Pest/Jute_Pest_Dataset/train'\n", "val_dir = 'LearningProjects/Jute Pest/Jute_Pest_Dataset/val'\n", "test_dir = 'LearningProjects/Jute Pest/Jute_Pest_Dataset/test'\n", "\n", "# Load the training, validation, and test datasets\n", "train_dataset = tf.keras.preprocessing.image_dataset_from_directory(\n", "    train_dir,\n", "    image_size=(640, 640),  # Resize the images to a common size\n", "    batch_size=None,          # Number of images per batch\n", "    label_mode='int',       # Labels as integers (for classification)\n", "    shuffle=True,           # Shuffle the dataset\n", ")\n", "\n", "val_dataset = tf.keras.preprocessing.image_dataset_from_directory(\n", "    val_dir,\n", "    image_size=(640, 640),\n", "    batch_size=None,\n", "    label_mode='int',\n", "    shuffle=True,\n", ")\n", "\n", "test_dataset = tf.keras.preprocessing.image_dataset_from_directory(\n", "    test_dir,\n", "    image_size=(640, 640),\n", "    batch_size=None,\n", "    label_mode='int',\n", "    shuffle=False,\n", ")\n", "\n", "# Check the structure and some data\n", "class_names = train_dataset.class_names\n", "print(\"Class names:\", class_names)\n", "print(\"Number of training samples:\", len(train_dataset.file_paths))\n", "print(\"Number of testing samples:\", len(test_dataset.file_paths))\n", "print(\"Number of validation samples:\", len(val_dataset.file_paths))"]}, {"cell_type": "code", "execution_count": 3, "id": "02e76666-ce83-4824-861a-f4440874adc4", "metadata": {}, "outputs": [], "source": ["model_url = \"https://tfhub.dev/google/bit/m-r101x1/1\"\n", "module = hub.KerasLayer(model_url)"]}, {"cell_type": "code", "execution_count": 4, "id": "3598e265-1d3a-450c-b915-92a424ea7e5c", "metadata": {}, "outputs": [], "source": ["class MyBiTModel(tf.keras.Model):\n", "  \"\"\"Bi<PERSON> with a new head.\"\"\"\n", "\n", "  def __init__(self, num_classes, module):\n", "    super().__init__()\n", "\n", "    self.num_classes = num_classes\n", "    self.head = tf.keras.layers.Dense(num_classes, kernel_initializer='zeros')\n", "    self.bit_model = module\n", "  \n", "  def call(self, images):\n", "    # No need to cut head off since we are using feature extractor model\n", "    bit_embedding = self.bit_model(images)\n", "    return self.head(bit_embedding)\n", "\n", "model = MyBiTModel(num_classes=17, module=module)"]}, {"cell_type": "code", "execution_count": 5, "id": "a9ec1a0b-2327-4151-abc1-6f76df6dd302", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Resize To: 512x512\n", "Crop To: 480x480\n", "Schedule Length: 500\n", "Schedule Boundaries: [200, 300, 400]\n"]}], "source": ["# Image size and dataset size configuration\n", "IMAGE_SIZE = \">96x96 px\"  # As our dataset images are 640x640 px\n", "DATASET_SIZE = \"<20k examples\"  # As our dataset has fewer than 20k examples\n", "\n", "# Preprocessing configuration\n", "if IMAGE_SIZE == \"=<96x96 px\":\n", "    RESIZE_TO = 160\n", "    CROP_TO = 128\n", "else:\n", "    RESIZE_TO = 512  # For images larger than 96x96px\n", "    CROP_TO = 480\n", "\n", "# Hyperparameters based on dataset size\n", "if DATASET_SIZE == \"<20k examples\":\n", "    SCHEDULE_LENGTH = 500  # Total training steps\n", "    SCHEDULE_BOUNDARIES = [200, 300, 400]  # Milestones for learning rate decay\n", "elif <PERSON>_SIZE == \"20k-500k examples\":\n", "    SCHEDULE_LENGTH = 10000\n", "    SCHEDULE_BOUNDARIES = [3000, 6000, 9000]\n", "else:\n", "    SCHEDULE_LENGTH = 20000\n", "    SCHEDULE_BOUNDARIES = [6000, 12000, 18000]\n", "\n", "# Print out the settings\n", "print(f\"Resize To: {RESIZE_TO}x{RESIZE_TO}\")\n", "print(f\"Crop To: {CROP_TO}x{CROP_TO}\")\n", "print(f\"Schedule Length: {SCHEDULE_LENGTH}\")\n", "print(f\"Schedule Boundaries: {SCHEDULE_BOUNDARIES}\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d3922221-2312-4d7e-ae93-ba650357db88", "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from keras.callbacks import EarlyStopping \n", "\n", "# Preprocessing helper functions\n", "BATCH_SIZE = 256 # 256 makes 25 batches in total\n", "SCHEDULE_LENGTH = SCHEDULE_LENGTH * 512 / BATCH_SIZE\n", "STEPS_PER_EPOCH = 7  # do 5 then 6 epochs only\n", "\n", "def cast_to_tuple(features):\n", "    \"\"\"Cast features to (image, label) tuple.\"\"\"\n", "    return features['image'], features['label']\n", "\n", "def preprocess_train(image, label):\n", "    \"\"\"Preprocess function for training dataset with augmentations.\"\"\"\n", "    # Apply augmentation to the image\n", "    image = tf.image.random_flip_left_right(image)  # Horizontal flip\n", "    image = tf.image.resize(image, [RESIZE_TO, RESIZE_TO])  # Resize\n", "    image = tf.image.random_crop(image, [CROP_TO, CROP_TO, 3])  # Random crop\n", "    image = tf.cast(image, tf.float32) / 255.0  # Normalize to [0, 1]\n", "    return image, label  # Return both image and label\n", "\n", "def preprocess_val_test(image, label):\n", "    \"\"\"Preprocess function for validation and test datasets (no augmentations).\"\"\"\n", "    image = tf.image.resize(image, [RESIZE_TO, RESIZE_TO])  # Resize\n", "    image = tf.cast(image, tf.float32) / 255.0  # Normalize to [0, 1]\n", "    return image, label\n", "\n", "def preprocess_test(image, label):\n", "    \"\"\"Preprocess function for test dataset (resize and normalize).\"\"\"\n", "    image = tf.image.resize(image, [RESIZE_TO, RESIZE_TO])  # Resize\n", "    image = tf.cast(image, tf.float32) / 255.0  # Normalize to [0, 1]\n", "    return image, label\n", "\n", "# Training dataset pipeline\n", "pipeline_train = (\n", "    train_dataset\n", "    .shuffle(1000)\n", "    .map(preprocess_train, num_parallel_calls=tf.data.AUTOTUNE)\n", "    .batch(BATCH_SIZE)\n", "    .repeat()\n", "    .prefetch(tf.data.AUTOTUNE)\n", ")\n", "\n", "# Validation dataset pipeline with repeat to ensure the entire dataset is evaluated\n", "pipeline_val = (\n", "    val_dataset\n", "    .map(preprocess_val_test, num_parallel_calls=tf.data.AUTOTUNE)\n", "    .batch(BATCH_SIZE)\n", "    .prefetch(tf.data.AUTOTUNE)\n", ")\n", "\n", "# Test dataset pipeline (no change)\n", "pipeline_test = (\n", "    test_dataset\n", "    .map(preprocess_test, num_parallel_calls=tf.data.AUTOTUNE)\n", "    .batch(BATCH_SIZE)\n", "    .prefetch(tf.data.AUTOTUNE)\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "0a067274-ab93-4cf9-a5ef-f6916dddac77", "metadata": {}, "outputs": [], "source": ["# Define optimiser and loss\n", "lr = 0.003 * BATCH_SIZE / 512 \n", "\n", "lr_schedule = tf.keras.optimizers.schedules.PiecewiseConstantDecay(\n", "    boundaries=SCHEDULE_BOUNDARIES, \n", "    values=[lr, lr*0.1, lr*0.001, lr*0.0001]\n", ")\n", "optimizer = tf.keras.optimizers.SGD(learning_rate=lr_schedule, momentum=0.9)\n", "\n", "loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "de03fa15-c093-44e9-8a5a-34db52669aee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/6\n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m88s\u001b[0m 11s/step - accuracy: 0.2605 - loss: 2.3945 - val_accuracy: 0.7046 - val_loss: 0.9666\n", "Epoch 2/6\n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m73s\u001b[0m 11s/step - accuracy: 0.8654 - loss: 0.6622 - val_accuracy: 0.8450 - val_loss: 0.4612\n", "Epoch 3/6\n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m71s\u001b[0m 10s/step - accuracy: 0.8997 - loss: 0.3363 - val_accuracy: 0.8814 - val_loss: 0.3479\n", "Epoch 4/6\n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m66s\u001b[0m 10s/step - accuracy: 0.9280 - loss: 0.2205 - val_accuracy: 0.8838 - val_loss: 0.3408\n", "Epoch 5/6\n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m72s\u001b[0m 11s/step - accuracy: 0.9591 - loss: 0.1499 - val_accuracy: 0.9128 - val_loss: 0.3012\n", "Epoch 6/6\n", "\u001b[1m7/7\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m73s\u001b[0m 11s/step - accuracy: 0.9769 - loss: 0.1153 - val_accuracy: 0.9031 - val_loss: 0.2963\n"]}], "source": ["model.compile(optimizer=optimizer,\n", "              loss=loss_fn,\n", "              metrics=['accuracy'])\n", "\n", "# Add EarlyStopping callback\n", "early_stopping = EarlyStopping(monitor='val_loss', patience=3, restore_best_weights=True)\n", "\n", "# Fine-tune model with early stopping\n", "history = model.fit(\n", "    pipeline_train,\n", "    batch_size=BATCH_SIZE,\n", "    steps_per_epoch=STEPS_PER_EPOCH,\n", "    epochs=6,  # or 10 depending on your plan\n", "    validation_data=pipeline_val,\n", "    callbacks=[early_stopping],\n", "    verbose=1\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "05a0d8d0-4fbe-45c1-ae35-8c86892ab82f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m2/2\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m14s\u001b[0m 5s/step - accuracy: 0.9597 - loss: 0.1340\n", "Test loss: 0.14026440680027008\n", "Test accuracy: 0.9551451206207275\n"]}], "source": ["# After training the model\n", "test_loss, test_accuracy = model.evaluate(pipeline_test)\n", "print(f\"Test loss: {test_loss}\")\n", "print(f\"Test accuracy: {test_accuracy}\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "078b7b57-1d6c-4652-9d62-86f5b879bd86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: my_saved_bit_model/assets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: my_saved_bit_model/assets\n"]}], "source": ["export_module_dir = 'my_saved_bit_model/'\n", "tf.saved_model.save(model, export_module_dir)"]}, {"cell_type": "code", "execution_count": 11, "id": "d87014d9-06da-4110-9c7a-794a643f9000", "metadata": {}, "outputs": [], "source": ["\n", "# that's all by <PERSON><PERSON>\n", "#----------------------------------------------------------------\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d171b98a-282d-4aec-8611-137c40919290", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}