import torch
from transformers import ResNetForImageClassification
from torchvision import datasets, transforms
from torchvision import transforms
from torch.utils.data import DataLoader
import tensorflow as tf
import tensorflow_hub as hub

import tensorflow_datasets as tfds

import time

from PIL import Image
import requests

import numpy as np

import os
import pathlib

# Define dataset directories
train_dir = 'LearningProjects/Jute Pest/Jute_Pest_Dataset/train'
val_dir = 'LearningProjects/Jute Pest/Jute_Pest_Dataset/val'
test_dir = 'LearningProjects/Jute Pest/Jute_Pest_Dataset/test'

# Load the training, validation, and test datasets
train_dataset = tf.keras.preprocessing.image_dataset_from_directory(
    train_dir,
    image_size=(640, 640),  # Resize the images to a common size
    batch_size=None,          # Number of images per batch
    label_mode='int',       # Labels as integers (for classification)
    shuffle=True,           # Shuffle the dataset
)

val_dataset = tf.keras.preprocessing.image_dataset_from_directory(
    val_dir,
    image_size=(640, 640),
    batch_size=None,
    label_mode='int',
    shuffle=True,
)

test_dataset = tf.keras.preprocessing.image_dataset_from_directory(
    test_dir,
    image_size=(640, 640),
    batch_size=None,
    label_mode='int',
    shuffle=False,
)

# Check the structure and some data
class_names = train_dataset.class_names
print("Class names:", class_names)
print("Number of training samples:", len(train_dataset.file_paths))
print("Number of testing samples:", len(test_dataset.file_paths))
print("Number of validation samples:", len(val_dataset.file_paths))

model_url = "https://tfhub.dev/google/bit/m-r101x1/1"
module = hub.KerasLayer(model_url)

class MyBiTModel(tf.keras.Model):
  """BiT with a new head."""

  def __init__(self, num_classes, module):
    super().__init__()

    self.num_classes = num_classes
    self.head = tf.keras.layers.Dense(num_classes, kernel_initializer='zeros')
    self.bit_model = module
  
  def call(self, images):
    # No need to cut head off since we are using feature extractor model
    bit_embedding = self.bit_model(images)
    return self.head(bit_embedding)

model = MyBiTModel(num_classes=17, module=module)

# Image size and dataset size configuration
IMAGE_SIZE = ">96x96 px"  # As our dataset images are 640x640 px
DATASET_SIZE = "<20k examples"  # As our dataset has fewer than 20k examples

# Preprocessing configuration
if IMAGE_SIZE == "=<96x96 px":
    RESIZE_TO = 160
    CROP_TO = 128
else:
    RESIZE_TO = 512  # For images larger than 96x96px
    CROP_TO = 480

# Hyperparameters based on dataset size
if DATASET_SIZE == "<20k examples":
    SCHEDULE_LENGTH = 500  # Total training steps
    SCHEDULE_BOUNDARIES = [200, 300, 400]  # Milestones for learning rate decay
elif DATASET_SIZE == "20k-500k examples":
    SCHEDULE_LENGTH = 10000
    SCHEDULE_BOUNDARIES = [3000, 6000, 9000]
else:
    SCHEDULE_LENGTH = 20000
    SCHEDULE_BOUNDARIES = [6000, 12000, 18000]

# Print out the settings
print(f"Resize To: {RESIZE_TO}x{RESIZE_TO}")
print(f"Crop To: {CROP_TO}x{CROP_TO}")
print(f"Schedule Length: {SCHEDULE_LENGTH}")
print(f"Schedule Boundaries: {SCHEDULE_BOUNDARIES}")


import tensorflow as tf
from keras.callbacks import EarlyStopping 

# Preprocessing helper functions
BATCH_SIZE = 256 # 256 makes 25 batches in total
SCHEDULE_LENGTH = SCHEDULE_LENGTH * 512 / BATCH_SIZE
STEPS_PER_EPOCH = 7  # do 5 then 6 epochs only

def cast_to_tuple(features):
    """Cast features to (image, label) tuple."""
    return features['image'], features['label']

def preprocess_train(image, label):
    """Preprocess function for training dataset with augmentations."""
    # Apply augmentation to the image
    image = tf.image.random_flip_left_right(image)  # Horizontal flip
    image = tf.image.resize(image, [RESIZE_TO, RESIZE_TO])  # Resize
    image = tf.image.random_crop(image, [CROP_TO, CROP_TO, 3])  # Random crop
    image = tf.cast(image, tf.float32) / 255.0  # Normalize to [0, 1]
    return image, label  # Return both image and label

def preprocess_val_test(image, label):
    """Preprocess function for validation and test datasets (no augmentations)."""
    image = tf.image.resize(image, [RESIZE_TO, RESIZE_TO])  # Resize
    image = tf.cast(image, tf.float32) / 255.0  # Normalize to [0, 1]
    return image, label

def preprocess_test(image, label):
    """Preprocess function for test dataset (resize and normalize)."""
    image = tf.image.resize(image, [RESIZE_TO, RESIZE_TO])  # Resize
    image = tf.cast(image, tf.float32) / 255.0  # Normalize to [0, 1]
    return image, label

# Training dataset pipeline
pipeline_train = (
    train_dataset
    .shuffle(1000)
    .map(preprocess_train, num_parallel_calls=tf.data.AUTOTUNE)
    .batch(BATCH_SIZE)
    .repeat()
    .prefetch(tf.data.AUTOTUNE)
)

# Validation dataset pipeline with repeat to ensure the entire dataset is evaluated
pipeline_val = (
    val_dataset
    .map(preprocess_val_test, num_parallel_calls=tf.data.AUTOTUNE)
    .batch(BATCH_SIZE)
    .prefetch(tf.data.AUTOTUNE)
)

# Test dataset pipeline (no change)
pipeline_test = (
    test_dataset
    .map(preprocess_test, num_parallel_calls=tf.data.AUTOTUNE)
    .batch(BATCH_SIZE)
    .prefetch(tf.data.AUTOTUNE)
)

# Define optimiser and loss
lr = 0.003 * BATCH_SIZE / 512 

lr_schedule = tf.keras.optimizers.schedules.PiecewiseConstantDecay(
    boundaries=SCHEDULE_BOUNDARIES, 
    values=[lr, lr*0.1, lr*0.001, lr*0.0001]
)
optimizer = tf.keras.optimizers.SGD(learning_rate=lr_schedule, momentum=0.9)

loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)


model.compile(optimizer=optimizer,
              loss=loss_fn,
              metrics=['accuracy'])

# Add EarlyStopping callback
early_stopping = EarlyStopping(monitor='val_loss', patience=3, restore_best_weights=True)

# Fine-tune model with early stopping
history = model.fit(
    pipeline_train,
    batch_size=BATCH_SIZE,
    steps_per_epoch=STEPS_PER_EPOCH,
    epochs=6,  # or 10 depending on your plan
    validation_data=pipeline_val,
    callbacks=[early_stopping],
    verbose=1
)

# After training the model
test_loss, test_accuracy = model.evaluate(pipeline_test)
print(f"Test loss: {test_loss}")
print(f"Test accuracy: {test_accuracy}")


export_module_dir = 'my_saved_bit_model/'
tf.saved_model.save(model, export_module_dir)


# that's all by Vansh Oberoi
#----------------------------------------------------------------



