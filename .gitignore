# Jute Pest Classification - Git Ignore File

# ============================================================================
# Python
# ============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ============================================================================
# Machine Learning & Data Science
# ============================================================================

# TensorFlow
# *.pb  # Commented out - you might want to keep saved_model.pb
checkpoint  # TensorFlow checkpoint files (not directories)
model.ckpt*
events.out.tfevents.*

# PyTorch
*.pth
*.pt

# Keras
*.h5
*.hdf5

# Scikit-learn
*.pkl
*.pickle

# Weights & Biases
wandb/

# MLflow
mlruns/

# TensorBoard logs
logs/
tensorboard_logs/

# Model checkpoints and saved models (uncomment lines below if you want to ignore them)
# checkpoints/          # Uncomment to ignore training checkpoints
# saved_models/         # Uncomment to ignore saved models
# models/               # Uncomment to ignore model directories
# *.model               # Uncomment to ignore .model files

# Keep important model files by default - comment out lines above if you want to ignore them

# Data files (uncomment if you want to ignore datasets)
# *.csv
# *.json
# *.parquet
# *.feather
# data/
# datasets/

# ============================================================================
# Streamlit
# ============================================================================

# Streamlit cache
.streamlit/

# ============================================================================
# IDE & Editors
# ============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# Operating System
# ============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# Project Specific
# ============================================================================

# Large model files (uncomment if models are too large for git)
# my_saved_bit_model/
# *.tflite
# *.onnx

# Temporary files
temp/
tmp/
*.tmp

# Configuration files with sensitive data
config.ini
secrets.json
.secrets

# Output files
output/
results/
predictions/

# Cache directories
cache/
.cache/

# ============================================================================
# Miscellaneous
# ============================================================================

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Log files
*.log

# Lock files
*.lock

# Temporary directories
temp*/
tmp*/

# Data
Jute_Pest_Dataset/