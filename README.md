# Jute Pest Classification Streamlit App

A web application for classifying 17 different types of jute pests using a trained BiT (Big Transfer) model.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- TensorFlow 2.13+
- Streamlit

### Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the Streamlit app:
```bash
streamlit run app.py
```

3. Open your browser and go to `http://localhost:8502`

## 📁 Project Structure

```
Jute Pest/
├── app.py                    # Main Streamlit application
├── requirements.txt          # Python dependencies
├── save_model_properly.py    # Helper script for saving models
├── fix_model.py             # Model fixing utilities
├── my_saved_bit_model/      # Trained model directory (needs to be complete)
└── README.md                # This file
```

## 🐛 Pest Types Supported

The model can classify the following 17 jute pest types:

1. Beet Armyworm
2. Black Hairy
3. Cutworm
4. Field Cricket
5. Jute Aphid
6. Jute Hairy
7. Jute Red Mite
8. Jute Semilooper
9. Jute Stem Girdler
10. Jute Stem Weevil
11. Leaf Beetle
12. Mealybug
13. Pod Borer
14. Scopula Emissaria
15. Termite
16. Termite odontotermes (Rambur)
17. Yellow Mite

## 🔧 Troubleshooting

### Model Loading Issues

If you see an error like "Failed to load the model", it means your model files are incomplete or missing. Here's how to fix it:

#### Option 1: Re-save your model from the notebook

1. Open your Jupyter notebook where you trained the model
2. After training, add this code to properly save the model:

```python
# Save the model properly
import tensorflow as tf

# Assuming your trained model is called 'model'
tf.saved_model.save(model, 'my_saved_bit_model')
print("Model saved successfully!")
```

#### Option 2: Use the helper script

1. Copy the `save_model_properly.py` script into your notebook directory
2. In your notebook, after training:

```python
from save_model_properly import save_model_for_streamlit

# Save the model (replace 'model' with your actual model variable)
success = save_model_for_streamlit(model, 'my_saved_bit_model')

if success:
    print("✅ Model saved! You can now run the Streamlit app.")
else:
    print("❌ Failed to save model. Check the error messages.")
```

#### Option 3: Check model files

Your `my_saved_bit_model` directory should contain:
- `saved_model.pb`
- `variables/` directory with:
  - `variables.index`
  - `variables.data-00000-of-00001` (or similar)

### Common Issues

1. **"No module named 'six.moves'"**
   ```bash
   pip install --upgrade six
   ```

2. **"No module named 'tensorflow_hub'"**
   ```bash
   pip install tensorflow_hub
   ```

3. **Model accuracy seems low**
   - Make sure you're using images of the correct size (512x512)
   - Ensure good image quality and lighting
   - The pest should be clearly visible in the image

## 🎯 Features

- **Easy Upload**: Drag and drop or browse to upload images
- **Real-time Classification**: Instant pest identification
- **Confidence Scores**: See how confident the model is
- **Top 3 Predictions**: View alternative possibilities
- **Detailed Results**: Complete probability breakdown for all classes
- **User-friendly Interface**: Clean, intuitive design

## 📊 Model Performance

- **Test Accuracy**: 95.5%
- **Model Architecture**: BiT-M R101x1
- **Input Size**: 512x512 pixels
- **Training Classes**: 17 jute pest types

## 🛠️ Development

### Adding New Features

1. **New Pest Types**: Update the `CLASS_NAMES` list in `app.py`
2. **Different Model**: Replace the model loading logic in the `load_model()` function
3. **UI Improvements**: Modify the Streamlit interface in the `main()` function

### Testing

Run the helper script to verify your model:
```bash
python save_model_properly.py
```

## 📝 Notes

- The app expects images in common formats (JPG, JPEG, PNG, BMP)
- Images are automatically resized to 512x512 pixels
- The model works best with clear, well-lit images of individual pests
- Processing time depends on your hardware (typically 1-3 seconds)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and research purposes.

---

**Need Help?** If you're still having issues, check that:
1. All dependencies are installed correctly
2. Your model was trained and saved properly
3. The model files are complete and not corrupted
4. You're using compatible versions of TensorFlow and related libraries
